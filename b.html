<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centris - Revolutionize Your Document Processing</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Centris Color Palette */
            --color-primary: #2563eb;
            --color-secondary: #0ea5e9;
            --color-accent: #22d3ee;
            --color-background: #f9fafb;
            --color-surface: #ffffff;
            --color-text: #111827;
            --color-text-muted: #4b5563;
            --color-border: #e5e7eb;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--color-text);
            background: var(--color-background);
            overflow-x: hidden;
        }

        /* Custom Cursor */
        .custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: var(--color-primary);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: transform 0.1s ease;
            mix-blend-mode: difference;
        }

        .custom-cursor.active {
            transform: scale(1.5);
            background: var(--color-accent);
        }

        /* 3D Background Canvas */
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1;
            opacity: 0.4;
            pointer-events: none;
        }

        /* Header */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(229, 231, 235, 0.3);
            transition: all 0.3s ease;
        }

        .header-scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--color-primary);
            text-decoration: none;
            transition: transform 0.3s ease;
            z-index: 1001;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 900;
            transition: transform 0.3s ease;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            text-decoration: none;
            color: var(--color-text);
            font-weight: 500;
            position: relative;
            transition: color 0.3s ease;
            padding: 0.5rem 0;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .nav-link:hover {
            color: var(--color-primary);
        }

        .cta-button {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 120px 2rem 2rem;
            position: relative;
            background: var(--color-background);
            z-index: 10;
        }

        .hero-content {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            width: 100%;
        }

        .hero-text {
            max-width: 600px;
            z-index: 20;
        }

        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: var(--color-text);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: var(--color-text-muted);
            margin-bottom: 2rem;
            line-height: 1.7;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .primary-button {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            padding: 1rem 2rem;
            border-radius: 16px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .primary-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(37, 99, 235, 0.4);
        }

        .secondary-button {
            background: transparent;
            color: var(--color-primary);
            padding: 1rem 2rem;
            border: 2px solid var(--color-primary);
            border-radius: 16px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .secondary-button:hover {
            background: var(--color-primary);
            color: white;
            transform: translateY(-3px);
        }

        /* Hero Visual */
        .hero-visual {
            position: relative;
            height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-elements {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .floating-card {
            position: absolute;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.5);
            transition: transform 0.3s ease;
        }

        .floating-card:hover {
            transform: translateY(-10px) scale(1.05);
        }

        .card-1 {
            top: 10%;
            left: 0%;
            width: 200px;
            animation: float1 6s ease-in-out infinite;
        }

        .card-2 {
            top: 50%;
            right: 10%;
            width: 180px;
            animation: float2 8s ease-in-out infinite;
        }

        .card-3 {
            bottom: 10%;
            left: 30%;
            width: 160px;
            animation: float3 7s ease-in-out infinite;
        }

        @keyframes float1 {
            0%, 100% { transform: translateY(0px) translateX(0px); }
            50% { transform: translateY(-20px) translateX(10px); }
        }

        @keyframes float2 {
            0%, 100% { transform: translateY(0px) translateX(0px); }
            50% { transform: translateY(-15px) translateX(-10px); }
        }

        @keyframes float3 {
            0%, 100% { transform: translateY(0px) translateX(0px); }
            50% { transform: translateY(-25px) translateX(15px); }
        }

        /* Stats Section */
        .stats {
            padding: 6rem 2rem;
            background: var(--color-surface);
            position: relative;
            z-index: 10;
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 3rem;
        }

        .stat-card {
            text-align: center;
            padding: 2rem;
            border-radius: 20px;
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(14, 165, 233, 0.05));
            border: 1px solid rgba(37, 99, 235, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--color-text-muted);
            font-weight: 500;
        }

        /* Features Section */
        .features {
            padding: 8rem 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            z-index: 10;
            position: relative;
        }

        .features-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-header {
            text-align: center;
            margin-bottom: 5rem;
        }

        .section-title {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1rem;
            color: var(--color-text);
        }

        .section-subtitle {
            font-size: 1.25rem;
            color: var(--color-text-muted);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: var(--color-surface);
            border-radius: 24px;
            padding: 3rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(37, 99, 235, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 80px rgba(37, 99, 235, 0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--color-text);
        }

        .feature-description {
            color: var(--color-text-muted);
            line-height: 1.7;
        }

        /* CTA Section */
        .cta-section {
            padding: 8rem 2rem;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
            z-index: 10;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 10;
        }

        .cta-title {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1.5rem;
        }

        .cta-text {
            font-size: 1.25rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .white-button {
            background: white;
            color: var(--color-primary);
            padding: 1rem 2rem;
            border-radius: 16px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .white-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        /* Mobile Menu */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--color-primary);
            cursor: pointer;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }

            .hero-visual {
                height: 300px;
                order: -1;
            }

            .floating-card {
                width: 150px !important;
                padding: 1rem;
            }

            .card-1 {
                top: 5%;
                left: 5%;
            }

            .card-2 {
                top: 40%;
                right: 5%;
            }

            .card-3 {
                bottom: 5%;
                left: 25%;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .section-title {
                font-size: 2rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .cta-title {
                font-size: 2rem;
            }
        }

        /* Loading animation */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--color-background);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }

        .loader {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(37, 99, 235, 0.1);
            border-left: 4px solid var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Animation classes */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
        }

        .scale-in {
            opacity: 0;
            transform: scale(0.8);
        }
    </style>
</head>
<body>
    <!-- Custom Cursor -->
    <div class="custom-cursor"></div>

    <!-- Loading Screen -->
    <div class="loading" id="loading">
        <div class="loader"></div>
    </div>

    <!-- 3D Background Canvas -->
    <canvas id="bg-canvas"></canvas>

    <!-- Header -->
    <header id="header">
        <nav class="navbar">
            <a href="#" class="logo">
                <div class="logo-icon">C</div>
                <span>CENTRIS</span>
            </a>
            <ul class="nav-menu">
                <li><a href="#" class="nav-link">Products</a></li>
                <li><a href="#" class="nav-link">Contact & Support</a></li>
                <li><a href="#" class="nav-link">About Us</a></li>
                <li><a href="#" class="nav-link">Careers</a></li>
            </ul>
            <a href="#" class="cta-button">Contact Us</a>
            <button class="mobile-menu-toggle">☰</button>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title fade-in">Revolutionize Your Document Processing</h1>
                <p class="hero-subtitle fade-in">Centris, the leading-edge electronic document processing system, transforms the way you manage and organize all kinds of your documents. Say goodbye to manual processing and data extraction, and embrace the future with Centris!</p>
                <div class="hero-actions fade-in">
                    <a href="#" class="primary-button">Get Started Today</a>
                    <a href="#" class="secondary-button">Watch Demo</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="floating-elements">
                    <div class="floating-card card-1">
                        <div style="width: 100%; height: 80px; background: linear-gradient(135deg, #e3f2fd, #bbdefb); border-radius: 8px; margin-bottom: 1rem;"></div>
                        <div style="height: 8px; background: #e0e0e0; border-radius: 4px; margin-bottom: 4px;"></div>
                        <div style="height: 8px; background: #e0e0e0; border-radius: 4px; width: 70%;"></div>
                    </div>
                    <div class="floating-card card-2">
                        <div style="width: 100%; height: 70px; background: linear-gradient(135deg, #f3e5f5, #e1bee7); border-radius: 8px; margin-bottom: 1rem;"></div>
                        <div style="height: 6px; background: #e0e0e0; border-radius: 3px; margin-bottom: 3px;"></div>
                        <div style="height: 6px; background: #e0e0e0; border-radius: 3px; width: 80%;"></div>
                    </div>
                    <div class="floating-card card-3">
                        <div style="width: 100%; height: 60px; background: linear-gradient(135deg, #e8f5e8, #c8e6c9); border-radius: 8px; margin-bottom: 1rem;"></div>
                        <div style="height: 6px; background: #e0e0e0; border-radius: 3px; margin-bottom: 3px;"></div>
                        <div style="height: 6px; background: #e0e0e0; border-radius: 3px; width: 60%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="stats-container">
            <div class="stat-card scale-in">
                <div class="stat-number">99.8%</div>
                <div class="stat-label">Accuracy Rate</div>
            </div>
            <div class="stat-card scale-in">
                <div class="stat-number">500K+</div>
                <div class="stat-label">Documents Processed Daily</div>
            </div>
            <div class="stat-card scale-in">
                <div class="stat-number">2.5s</div>
                <div class="stat-label">Average Processing Time</div>
            </div>
            <div class="stat-card scale-in">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Support Available</div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="features-container">
            <div class="section-header">
                <h2 class="section-title fade-in">Powerful Features</h2>
                <p class="section-subtitle fade-in">Discover how Centris transforms your document workflow with cutting-edge technology</p>
            </div>
            <div class="features-grid">
                <div class="feature-card fade-in">
                    <div class="feature-icon">🤖</div>
                    <h3 class="feature-title">AI-Powered Processing</h3>
                    <p class="feature-description">Advanced machine learning algorithms automatically extract, classify, and organize your documents with unprecedented accuracy.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">⚡</div>
                    <h3 class="feature-title">Lightning Fast</h3>
                    <p class="feature-description">Process thousands of documents in seconds with our optimized processing engine built for enterprise-scale operations.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">🔒</div>
                    <h3 class="feature-title">Enterprise Security</h3>
                    <p class="feature-description">Bank-grade encryption and compliance with international standards ensure your sensitive documents are always protected.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">🌐</div>
                    <h3 class="feature-title">Cloud Integration</h3>
                    <p class="feature-description">Seamlessly integrate with your existing cloud infrastructure and access your documents from anywhere, anytime.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">Advanced Analytics</h3>
                    <p class="feature-description">Get detailed insights into your document processing with comprehensive analytics and reporting tools.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">🔧</div>
                    <h3 class="feature-title">Easy Integration</h3>
                    <p class="feature-description">Connect with your existing systems through our robust API and extensive integration capabilities.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-container">
            <h2 class="cta-title fade-in">Ready to Transform Your Business?</h2>
            <p class="cta-text fade-in">Join thousands of companies already using Centris to revolutionize their document processing workflow.</p>
            <div class="cta-buttons fade-in">
                <a href="#" class="white-button">Start Free Trial</a>
                <a href="#" class="white-button">Schedule Demo</a>
            </div>
        </div>
    </section>

    <script>
        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger);

        // Custom cursor
        const cursor = document.querySelector('.custom-cursor');
        const links = document.querySelectorAll('a, button');

        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
        });

        links.forEach(link => {
            link.addEventListener('mouseenter', () => cursor.classList.add('active'));
            link.addEventListener('mouseleave', () => cursor.classList.remove('active'));
        });

        // 3D Background Scene
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ 
            canvas: document.getElementById('bg-canvas'),
            alpha: true,
            antialias: true
        });

        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000, 0);

        // Create geometric shapes
        const geometries = [
            new THREE.BoxGeometry(1, 1, 1),
            new THREE.SphereGeometry(0.7, 32, 32),
            new THREE.ConeGeometry(0.7, 1.5, 32),
            new THREE.OctahedronGeometry(0.8)
        ];

        const materials = [
            new THREE.MeshLambertMaterial({ color: 0x2563eb, transparent: true, opacity: 0.6 }),
            new THREE.MeshLambertMaterial({ color: 0x0ea5e9, transparent: true, opacity: 0.6 }),
            new THREE.MeshLambertMaterial({ color: 0x22d3ee, transparent: true, opacity: 0.6 }),
            new THREE.MeshLambertMaterial({ color: 0x6366f1, transparent: true, opacity: 0.6 })
        ];

        const shapes = [];
        for (let i = 0; i < 15; i++) {
            const geometry = geometries[Math.floor(Math.random() * geometries.length)];
            const material = materials[Math.floor(Math.random() * materials.length)];
            const mesh = new THREE.Mesh(geometry, material);
            
            mesh.position.x = (Math.random() - 0.5) * 50;
            mesh.position.y = (Math.random() - 0.5) * 50;
            mesh.position.z = (Math.random() - 0.5) * 50;
            
            mesh.rotation.x = Math.random() * Math.PI;
            mesh.rotation.y = Math.random() * Math.PI;
            
            scene.add(mesh);
            shapes.push(mesh);
        }

        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        scene.add(directionalLight);

        camera.position.z = 30;

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            shapes.forEach((shape, index) => {
                shape.rotation.x += 0.005 + index * 0.0001;
                shape.rotation.y += 0.005 + index * 0.0001;
                shape.position.y += Math.sin(Date.now() * 0.001 + index) * 0.002;
            });
            
            renderer.render(scene, camera);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Header scroll effect
        const header = document.getElementById('header');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                header.classList.add('header-scrolled');
            } else {
                header.classList.remove('header-scrolled');
            }
        });

        // GSAP Animations
        function initAnimations() {
            // Hero animations
            gsap.timeline()
                .from('.hero-title', { duration: 1, y: 50, opacity: 0, ease: "power3.out" })
                .from('.hero-subtitle', { duration: 1, y: 30, opacity: 0, ease: "power3.out" }, "-=0.5")
                .from('.hero-actions', { duration: 1, y: 30, opacity: 0, ease: "power3.out" }, "-=0.5")
                .from('.floating-card', { 
                    duration: 1.5, 
                    scale: 0, 
                    opacity: 0, 
                    stagger: 0.2,
                    ease: "back.out(1.7)"
                }, "-=1");

            // Stats animation
            gsap.fromTo('.stat-card', 
                { scale: 0, opacity: 0 },
                {
                    scale: 1,
                    opacity: 1,
                    duration: 0.8,
                    stagger: 0.2,
                    ease: "back.out(1.7)",
                    scrollTrigger: {
                        trigger: '.stats',
                        start: 'top 80%'
                    }
                }
            );

            // Section headers
            gsap.fromTo('.section-title',
                { y: 50, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.section-title',
                        start: 'top 85%'
                    }
                }
            );

            gsap.fromTo('.section-subtitle',
                { y: 30, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    delay: 0.2,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.section-subtitle',
                        start: 'top 85%'
                    }
                }
            );

            // Feature cards animation
            gsap.fromTo('.feature-card',
                { y: 50, opacity: 0, scale: 0.9 },
                {
                    y: 0,
                    opacity: 1,
                    scale: 1,
                    duration: 0.8,
                    stagger: 0.15,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.features-grid',
                        start: 'top 80%'
                    }
                }
            );

            // CTA section animation
            gsap.fromTo('.cta-title',
                { scale: 0.8, opacity: 0 },
                {
                    scale: 1,
                    opacity: 1,
                    duration: 1,
                    ease: "back.out(1.7)",
                    scrollTrigger: {
                        trigger: '.cta-section',
                        start: 'top 80%'
                    }
                }
            );

            gsap.fromTo('.cta-text',
                { y: 30, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    delay: 0.3,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.cta-section',
                        start: 'top 80%'
                    }
                }
            );

            gsap.fromTo('.cta-buttons',
                { y: 30, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    delay: 0.6,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.cta-section',
                        start: 'top 80%'
                    }
                }
            );

            // Parallax effect for floating cards
            gsap.to('.card-1', {
                y: -50,
                ease: "none",
                scrollTrigger: {
                    trigger: '.hero',
                    start: 'top top',
                    end: 'bottom top',
                    scrub: 1
                }
            });

            gsap.to('.card-2', {
                y: -80,
                ease: "none",
                scrollTrigger: {
                    trigger: '.hero',
                    start: 'top top',
                    end: 'bottom top',
                    scrub: 1
                }
            });

            gsap.to('.card-3', {
                y: -60,
                ease: "none",
                scrollTrigger: {
                    trigger: '.hero',
                    start: 'top top',
                    end: 'bottom top',
                    scrub: 1
                }
            });
        }

        // Microinteractions
        function initMicrointeractions() {
            // Button hover effects
            document.querySelectorAll('.primary-button, .secondary-button, .cta-button, .white-button').forEach(button => {
                button.addEventListener('mouseenter', () => {
                    gsap.to(button, { duration: 0.3, scale: 1.05, ease: "power2.out" });
                });
                
                button.addEventListener('mouseleave', () => {
                    gsap.to(button, { duration: 0.3, scale: 1, ease: "power2.out" });
                });
            });

            // Feature card hover effects
            document.querySelectorAll('.feature-card').forEach(card => {
                const icon = card.querySelector('.feature-icon');
                
                card.addEventListener('mouseenter', () => {
                    gsap.to(icon, { duration: 0.3, scale: 1.1, ease: "power2.out" });
                });
                
                card.addEventListener('mouseleave', () => {
                    gsap.to(icon, { duration: 0.3, scale: 1, ease: "power2.out" });
                });
            });

            // Logo hover effect
            const logoIcon = document.querySelector('.logo-icon');
            const logo = document.querySelector('.logo');
            
            logo.addEventListener('mouseenter', () => {
                gsap.to(logoIcon, { duration: 0.5, rotation: 360, ease: "power2.out" });
            });

            // Nav link hover effects
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('mouseenter', () => {
                    gsap.to(link, { duration: 0.3, y: -2, ease: "power2.out" });
                });
                
                link.addEventListener('mouseleave', () => {
                    gsap.to(link, { duration: 0.3, y: 0, ease: "power2.out" });
                });
            });

            // Floating cards interactive movement
            document.querySelectorAll('.floating-card').forEach(card => {
                card.addEventListener('mouseenter', () => {
                    gsap.to(card, { 
                        duration: 0.3, 
                        y: -20, 
                        scale: 1.05, 
                        ease: "power2.out" 
                    });
                });
                
                card.addEventListener('mouseleave', () => {
                    gsap.to(card, { 
                        duration: 0.3, 
                        y: 0, 
                        scale: 1, 
                        ease: "power2.out" 
                    });
                });
            });
        }

        // Mouse movement parallax
        function initParallax() {
            document.addEventListener('mousemove', (e) => {
                const mouseX = (e.clientX / window.innerWidth) - 0.5;
                const mouseY = (e.clientY / window.innerHeight) - 0.5;

                gsap.to('.floating-card', {
                    duration: 1,
                    x: mouseX * 15,
                    y: mouseY * 15,
                    ease: "power2.out",
                    stagger: 0.02
                });
            });
        }

        // Loading screen
        function hideLoadingScreen() {
            gsap.to('#loading', {
                duration: 0.5,
                opacity: 0,
                ease: "power2.out",
                onComplete: () => {
                    document.getElementById('loading').style.display = 'none';
                }
            });
        }

        // Initialize everything
        function init() {
            animate();
            initAnimations();
            initMicrointeractions();
            initParallax();
            
            // Hide loading screen after everything is ready
            setTimeout(hideLoadingScreen, 1000);
        }

        // Start when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Performance optimization
        let ticking = false;
        function updateParallax() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', updateParallax, { passive: true });
    </script>
</body>
</html>