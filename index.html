<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centris - Revolutionize Your Document Processing</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/TextPlugin.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Color Palette from Centris Logo */
            --color-primary: #2563eb;
            --color-secondary: #0ea5e9;
            --color-accent: #22d3ee;
            --color-background: #f9fafb;
            --color-surface: #ffffff;
            --color-text: #111827;
            --color-text-muted: #4b5563;
            --color-border: #e5e7eb;
            
            /* Dark mode colors */
            --color-primary-dark: #3b82f6;
            --color-secondary-dark: #38bdf8;
            --color-accent-dark: #67e8f9;
            --color-background-dark: #030712;
            --color-surface-dark: #111827;
            --color-text-dark: #f9fafb;
            --color-text-muted-dark: #9ca3af;
            --color-border-dark: #1f2937;

            /* OKLCH values for v4 compatibility */
            --primary-oklch: oklch(0.608 0.279 245.831);
            --secondary-oklch: oklch(0.714 0.123 238.75);
            --accent-oklch: oklch(0.821 0.135 195.198);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--color-text);
            background: var(--color-background);
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        /* 3D Background Canvas */
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            z-index: -1;
            opacity: 0.6;
        }

        /* Custom Cursor */
        .custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: var(--color-primary);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: transform 0.1s ease;
            mix-blend-mode: difference;
        }

        .custom-cursor.active {
            transform: scale(1.5);
            background: var(--color-accent);
        }

        /* Header */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(229, 231, 235, 0.3);
            transition: all 0.3s ease;
        }

        .header-scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .navbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--color-primary);
            text-decoration: none;
            transition: transform 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 900;
            transition: transform 0.3s ease;
        }

        .logo:hover .logo-icon {
            transform: rotate(360deg);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            text-decoration: none;
            color: var(--color-text);
            font-weight: 500;
            position: relative;
            transition: color 0.3s ease;
            padding: 0.5rem 0;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .nav-link:hover {
            color: var(--color-primary);
        }

        .cta-button {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            z-index: 10;
        }

        .hero-text {
            max-width: 600px;
        }

        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4rem);
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: #111827 !important;
            opacity: 1 !important;
            /* Force text to be visible */
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: #4b5563 !important;
            margin-bottom: 2rem;
            line-height: 1.7;
            opacity: 1 !important;
            font-weight: 400;
            /* Force subtitle to be visible */
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            opacity: 1 !important;
        }

        .primary-button {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            padding: 1rem 2rem;
            border-radius: 16px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
        }

        .primary-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(37, 99, 235, 0.4);
        }

        .secondary-button {
            background: transparent;
            color: var(--color-primary);
            padding: 1rem 2rem;
            border: 2px solid var(--color-primary);
            border-radius: 16px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .secondary-button:hover {
            background: var(--color-primary);
            color: white;
            transform: translateY(-3px);
        }

        /* 3D Visual Container */
        .hero-visual {
            position: relative;
            height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
            /* Allow cards to be visible */
        }

        .floating-elements {
            position: absolute;
            inset: 0;
            z-index: 5;
            pointer-events: none;
        }

        .floating-card {
            position: absolute;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease;
            pointer-events: auto;
        }

        .floating-card:hover {
            transform: translateY(-10px) scale(1.05);
            z-index: 15;
        }

        .card-1 {
            top: 5%;
            left: 5%;
            width: 160px;
            animation: float 6s ease-in-out infinite;
            z-index: 3;
        }

        .card-2 {
            top: 15%;
            right: 5%;
            width: 140px;
            animation: float 6s ease-in-out infinite reverse;
            animation-delay: -2s;
            z-index: 2;
        }

        .card-3 {
            bottom: 5%;
            right: 30%;
            width: 120px;
            animation: float 6s ease-in-out infinite;
            animation-delay: -4s;
            z-index: 1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(2deg); }
        }

        /* Stats Section */
        .stats {
            padding: 6rem 2rem;
            background: var(--color-surface);
            position: relative;
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 3rem;
        }

        .stat-card {
            text-align: center;
            padding: 2rem;
            border-radius: 20px;
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(14, 165, 233, 0.05));
            border: 1px solid rgba(37, 99, 235, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--color-text-muted);
            font-weight: 500;
        }

        /* Features Grid */
        .features {
            padding: 8rem 3rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .features-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-header {
            text-align: center;
            margin-bottom: 5rem;
        }

        .section-title {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--color-text), var(--color-primary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-subtitle {
            font-size: 1.25rem;
            color: var(--color-text-muted);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        @media (max-width: 1200px) {
            .features-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }
        }

        .feature-card {
            background: var(--color-surface);
            border-radius: 24px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(37, 99, 235, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            height: auto;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 80px rgba(37, 99, 235, 0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--color-text);
        }

        .feature-description {
            color: var(--color-text-muted);
            line-height: 1.7;
        }

        /* CTA Section */
        .cta-section {
            padding: 8rem 2rem;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 10;
        }

        .cta-title {
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 1.5rem;
        }

        .cta-text {
            font-size: 1.25rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .white-button {
            background: white;
            color: var(--color-primary);
            padding: 1rem 2rem;
            border-radius: 16px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .white-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .hero-visual {
                height: 400px;
                order: -1;
            }

            .features-grid {
                grid-template-columns: 1fr !important;
                gap: 1.5rem;
                max-width: 100%;
                padding: 0 1rem;
            }

            .feature-card {
                padding: 1.5rem;
                margin-bottom: 1rem;
            }

            .section-title {
                font-size: 2rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }
        }

        /* Smooth scroll animations - removed opacity 0 */
        .fade-in {
            transform: translateY(30px);
        }

        .slide-in-left {
            transform: translateX(-50px);
        }

        .slide-in-right {
            transform: translateX(50px);
        }

        .scale-in {
            transform: scale(0.8);
        }

        /* Loading animation */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--color-background);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }

        .loader {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(37, 99, 235, 0.1);
            border-left: 4px solid var(--color-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Custom Cursor -->
    <div class="custom-cursor"></div>

    <!-- Loading Screen -->
    <div class="loading" id="loading">
        <div class="loader"></div>
    </div>

    <!-- 3D Background Canvas -->
    <canvas id="bg-canvas"></canvas>

    <!-- Header -->
    <header id="header">
        <nav class="navbar">
            <a href="#" class="logo">
                <div class="logo-icon">C</div>
                <span>CENTRIS</span>
            </a>
            <ul class="nav-menu">
                <li><a href="#" class="nav-link">Products</a></li>
                <li><a href="#" class="nav-link">Contact & Support</a></li>
                <li><a href="#" class="nav-link">About Us</a></li>
                <li><a href="#" class="nav-link">Careers</a></li>
            </ul>
            <a href="#" class="cta-button">Contact Us</a>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title fade-in">Revolutionize Your Document Processing</h1>
                <p class="hero-subtitle fade-in">Centris, the leading-edge electronic document processing system, transforms the way you manage and organize all kinds of your documents. Say goodbye to manual processing and data extraction, and embrace the future with Centris!</p>
                <div class="hero-actions fade-in">
                    <a href="#" class="primary-button">Get Started Today</a>
                    <a href="#" class="secondary-button">Watch Demo</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="floating-elements">
                    <div class="floating-card card-1">
                        <div style="width: 100%; height: 100px; background: linear-gradient(135deg, #e3f2fd, #bbdefb); border-radius: 8px; margin-bottom: 1rem;"></div>
                        <div style="height: 8px; background: #e0e0e0; border-radius: 4px; margin-bottom: 4px;"></div>
                        <div style="height: 8px; background: #e0e0e0; border-radius: 4px; width: 70%;"></div>
                    </div>
                    <div class="floating-card card-2">
                        <div style="width: 100%; height: 80px; background: linear-gradient(135deg, #f3e5f5, #e1bee7); border-radius: 8px; margin-bottom: 1rem;"></div>
                        <div style="height: 6px; background: #e0e0e0; border-radius: 3px; margin-bottom: 3px;"></div>
                        <div style="height: 6px; background: #e0e0e0; border-radius: 3px; width: 80%;"></div>
                    </div>
                    <div class="floating-card card-3">
                        <div style="width: 100%; height: 70px; background: linear-gradient(135deg, #e8f5e8, #c8e6c9); border-radius: 8px; margin-bottom: 1rem;"></div>
                        <div style="height: 6px; background: #e0e0e0; border-radius: 3px; margin-bottom: 3px;"></div>
                        <div style="height: 6px; background: #e0e0e0; border-radius: 3px; width: 60%;"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="stats-container">
            <div class="stat-card scale-in">
                <div class="stat-number">99.8%</div>
                <div class="stat-label">Accuracy Rate</div>
            </div>
            <div class="stat-card scale-in">
                <div class="stat-number">500K+</div>
                <div class="stat-label">Documents Processed Daily</div>
            </div>
            <div class="stat-card scale-in">
                <div class="stat-number">2.5s</div>
                <div class="stat-label">Average Processing Time</div>
            </div>
            <div class="stat-card scale-in">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Support Available</div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="features-container">
            <div class="section-header">
                <h2 class="section-title fade-in">Powerful Features</h2>
                <p class="section-subtitle fade-in">Discover how Centris transforms your document workflow with cutting-edge technology</p>
            </div>
            <div class="features-grid">
                <div class="feature-card slide-in-left">
                    <div class="feature-icon">🤖</div>
                    <h3 class="feature-title">AI-Powered Processing</h3>
                    <p class="feature-description">Advanced machine learning algorithms automatically extract, classify, and organize your documents with unprecedented accuracy.</p>
                </div>
                <div class="feature-card slide-in-right">
                    <div class="feature-icon">⚡</div>
                    <h3 class="feature-title">Lightning Fast</h3>
                    <p class="feature-description">Process thousands of documents in seconds with our optimized processing engine built for enterprise-scale operations.</p>
                </div>
                <div class="feature-card slide-in-left">
                    <div class="feature-icon">🔒</div>
                    <h3 class="feature-title">Enterprise Security</h3>
                    <p class="feature-description">Bank-grade encryption and compliance with international standards ensure your sensitive documents are always protected.</p>
                </div>
                <div class="feature-card slide-in-right">
                    <div class="feature-icon">🌐</div>
                    <h3 class="feature-title">Cloud Integration</h3>
                    <p class="feature-description">Seamlessly integrate with your existing cloud infrastructure and access your documents from anywhere, anytime.</p>
                </div>
                <div class="feature-card slide-in-left">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">Advanced Analytics</h3>
                    <p class="feature-description">Get detailed insights into your document processing with comprehensive analytics and reporting tools.</p>
                </div>
                <div class="feature-card slide-in-right">
                    <div class="feature-icon">🔧</div>
                    <h3 class="feature-title">Easy Integration</h3>
                    <p class="feature-description">Connect with your existing systems through our robust API and extensive integration capabilities.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-container">
            <h2 class="cta-title fade-in">Ready to Transform Your Business?</h2>
            <p class="cta-text fade-in">Join thousands of companies already using Centris to revolutionize their document processing workflow.</p>
            <div class="cta-buttons fade-in">
                <a href="#" class="white-button">Start Free Trial</a>
                <a href="#" class="white-button">Schedule Demo</a>
            </div>
        </div>
    </section>

    <script>
        // Register ScrollTrigger plugin
        gsap.registerPlugin(ScrollTrigger, TextPlugin);

        // Custom cursor
        const cursor = document.querySelector('.custom-cursor');
        const links = document.querySelectorAll('a, button');

        document.addEventListener('mousemove', (e) => {
            cursor.style.left = e.clientX + 'px';
            cursor.style.top = e.clientY + 'px';
        });

        links.forEach(link => {
            link.addEventListener('mouseenter', () => cursor.classList.add('active'));
            link.addEventListener('mouseleave', () => cursor.classList.remove('active'));
        });

        // 3D Background Scene
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ 
            canvas: document.getElementById('bg-canvas'),
            alpha: true,
            antialias: true
        });

        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000, 0);

        // Create geometric shapes
        const geometries = [
            new THREE.BoxGeometry(1, 1, 1),
            new THREE.SphereGeometry(0.7, 32, 32),
            new THREE.ConeGeometry(0.7, 1.5, 32),
            new THREE.OctahedronGeometry(0.8)
        ];

        const materials = [
            new THREE.MeshLambertMaterial({ color: 0x2563eb, transparent: true, opacity: 0.6 }),
            new THREE.MeshLambertMaterial({ color: 0x0ea5e9, transparent: true, opacity: 0.6 }),
            new THREE.MeshLambertMaterial({ color: 0x22d3ee, transparent: true, opacity: 0.6 }),
            new THREE.MeshLambertMaterial({ color: 0x6366f1, transparent: true, opacity: 0.6 })
        ];

        const shapes = [];
        for (let i = 0; i < 20; i++) {
            const geometry = geometries[Math.floor(Math.random() * geometries.length)];
            const material = materials[Math.floor(Math.random() * materials.length)];
            const mesh = new THREE.Mesh(geometry, material);
            
            mesh.position.x = (Math.random() - 0.5) * 50;
            mesh.position.y = (Math.random() - 0.5) * 50;
            mesh.position.z = (Math.random() - 0.5) * 50;
            
            mesh.rotation.x = Math.random() * Math.PI;
            mesh.rotation.y = Math.random() * Math.PI;
            
            scene.add(mesh);
            shapes.push(mesh);
        }

        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        scene.add(directionalLight);

        camera.position.z = 30;

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            shapes.forEach((shape, index) => {
                shape.rotation.x += 0.005 + index * 0.0001;
                shape.rotation.y += 0.005 + index * 0.0001;
                shape.position.y += Math.sin(Date.now() * 0.001 + index) * 0.002;
            });
            
            renderer.render(scene, camera);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Header scroll effect
        const header = document.getElementById('header');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                header.classList.add('header-scrolled');
            } else {
                header.classList.remove('header-scrolled');
            }
        });

        // GSAP Animations
        function initAnimations() {
            // Hero animations - simplified without opacity changes
            gsap.timeline()
                .from('.hero-title', { duration: 1, y: 30, ease: "power3.out" })
                .from('.hero-subtitle', { duration: 1, y: 20, ease: "power3.out" }, "-=0.5")
                .from('.hero-actions', { duration: 1, y: 20, ease: "power3.out" }, "-=0.5")
                .from('.floating-card', {
                    duration: 1.5,
                    scale: 0.8,
                    rotation: 10,
                    stagger: 0.2,
                    ease: "back.out(1.7)"
                }, "-=1");

            // Stats animation
            gsap.fromTo('.stat-card', 
                { scale: 0, opacity: 0 },
                {
                    scale: 1,
                    opacity: 1,
                    duration: 0.8,
                    stagger: 0.2,
                    ease: "back.out(1.7)",
                    scrollTrigger: {
                        trigger: '.stats',
                        start: 'top 80%'
                    }
                }
            );

            // Animate stat numbers
            document.querySelectorAll('.stat-number').forEach(stat => {
                const text = stat.textContent;
                const number = text.match(/[\d.]+/)[0];
                const suffix = text.replace(number, '');
                
                ScrollTrigger.create({
                    trigger: stat,
                    start: 'top 80%',
                    onEnter: () => {
                        gsap.to(stat, {
                            duration: 2,
                            textContent: 0,
                            snap: { textContent: 1 },
                            ease: "power2.out",
                            onUpdate: function() {
                                const current = Math.ceil(this.targets()[0].textContent);
                                if (number.includes('.')) {
                                    stat.textContent = (current / 10).toFixed(1) + suffix;
                                } else {
                                    stat.textContent = current.toLocaleString() + suffix;
                                }
                            }
                        });
                        gsap.set(stat, { textContent: number + suffix });
                    }
                });
            });

            // Section header animations
            gsap.fromTo('.section-title',
                { y: 50, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.section-title',
                        start: 'top 85%'
                    }
                }
            );

            gsap.fromTo('.section-subtitle',
                { y: 30, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    delay: 0.2,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.section-subtitle',
                        start: 'top 85%'
                    }
                }
            );

            // Feature cards animation - without opacity changes
            gsap.fromTo('.feature-card',
                { y: 30, scale: 0.95 },
                {
                    y: 0,
                    scale: 1,
                    duration: 0.8,
                    stagger: 0.15,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.features-grid',
                        start: 'top 80%'
                    }
                }
            );

            // CTA section animation
            gsap.fromTo('.cta-title',
                { scale: 0.8, opacity: 0 },
                {
                    scale: 1,
                    opacity: 1,
                    duration: 1,
                    ease: "back.out(1.7)",
                    scrollTrigger: {
                        trigger: '.cta-section',
                        start: 'top 80%'
                    }
                }
            );

            gsap.fromTo('.cta-text',
                { y: 30, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    delay: 0.3,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.cta-section',
                        start: 'top 80%'
                    }
                }
            );

            gsap.fromTo('.cta-buttons',
                { y: 30, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    delay: 0.6,
                    ease: "power3.out",
                    scrollTrigger: {
                        trigger: '.cta-section',
                        start: 'top 80%'
                    }
                }
            );

            // Parallax effect for floating cards
            gsap.to('.card-1', {
                y: -100,
                rotation: 10,
                scrollTrigger: {
                    trigger: '.hero',
                    start: 'top top',
                    end: 'bottom top',
                    scrub: 1
                }
            });

            gsap.to('.card-2', {
                y: -150,
                rotation: -15,
                scrollTrigger: {
                    trigger: '.hero',
                    start: 'top top',
                    end: 'bottom top',
                    scrub: 1
                }
            });

            gsap.to('.card-3', {
                y: -120,
                rotation: 8,
                scrollTrigger: {
                    trigger: '.hero',
                    start: 'top top',
                    end: 'bottom top',
                    scrub: 1
                }
            });
        }

        // Microinteractions
        function initMicrointeractions() {
            // Button hover effects
            document.querySelectorAll('.primary-button, .secondary-button, .cta-button, .white-button').forEach(button => {
                button.addEventListener('mouseenter', () => {
                    gsap.to(button, { duration: 0.3, scale: 1.05, ease: "power2.out" });
                });
                
                button.addEventListener('mouseleave', () => {
                    gsap.to(button, { duration: 0.3, scale: 1, ease: "power2.out" });
                });

                button.addEventListener('mousedown', () => {
                    gsap.to(button, { duration: 0.1, scale: 0.95, ease: "power2.out" });
                });

                button.addEventListener('mouseup', () => {
                    gsap.to(button, { duration: 0.1, scale: 1.05, ease: "power2.out" });
                });
            });

            // Feature card hover effects
            document.querySelectorAll('.feature-card').forEach(card => {
                const icon = card.querySelector('.feature-icon');
                
                card.addEventListener('mouseenter', () => {
                    gsap.to(icon, { duration: 0.3, rotation: 360, scale: 1.1, ease: "power2.out" });
                });
                
                card.addEventListener('mouseleave', () => {
                    gsap.to(icon, { duration: 0.3, rotation: 0, scale: 1, ease: "power2.out" });
                });
            });

            // Logo hover effect
            const logoIcon = document.querySelector('.logo-icon');
            const logo = document.querySelector('.logo');
            
            logo.addEventListener('mouseenter', () => {
                gsap.to(logoIcon, { duration: 0.5, rotation: 360, ease: "power2.out" });
            });

            // Nav link hover effects
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('mouseenter', () => {
                    gsap.to(link, { duration: 0.3, y: -2, ease: "power2.out" });
                });
                
                link.addEventListener('mouseleave', () => {
                    gsap.to(link, { duration: 0.3, y: 0, ease: "power2.out" });
                });
            });

            // Floating cards interactive movement
            document.querySelectorAll('.floating-card').forEach(card => {
                card.addEventListener('mouseenter', () => {
                    gsap.to(card, { 
                        duration: 0.3, 
                        y: -20, 
                        scale: 1.05, 
                        rotationY: 5,
                        ease: "power2.out" 
                    });
                });
                
                card.addEventListener('mouseleave', () => {
                    gsap.to(card, { 
                        duration: 0.3, 
                        y: 0, 
                        scale: 1, 
                        rotationY: 0,
                        ease: "power2.out" 
                    });
                });
            });
        }

        // Mouse movement parallax
        function initParallax() {
            document.addEventListener('mousemove', (e) => {
                const mouseX = (e.clientX / window.innerWidth) - 0.5;
                const mouseY = (e.clientY / window.innerHeight) - 0.5;

                gsap.to('.floating-card', {
                    duration: 1,
                    x: mouseX * 20,
                    y: mouseY * 20,
                    rotationY: mouseX * 10,
                    rotationX: -mouseY * 10,
                    ease: "power2.out",
                    stagger: 0.02
                });

                gsap.to(shapes, {
                    duration: 2,
                    rotationY: mouseX * 0.5,
                    rotationX: -mouseY * 0.5,
                    ease: "power2.out",
                    stagger: 0.01
                });
            });
        }

        // Loading screen
        function hideLoadingScreen() {
            gsap.to('#loading', {
                duration: 0.5,
                opacity: 0,
                ease: "power2.out",
                onComplete: () => {
                    document.getElementById('loading').style.display = 'none';
                }
            });
        }

        // Text scramble effect for hero title
        function initTextEffects() {
            const heroTitle = document.querySelector('.hero-title');
            const originalText = heroTitle.textContent;
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            
            function scrambleText() {
                let iteration = 0;
                const interval = setInterval(() => {
                    heroTitle.textContent = originalText
                        .split('')
                        .map((letter, index) => {
                            if (index < iteration) {
                                return originalText[index];
                            }
                            return letters[Math.floor(Math.random() * 26)];
                        })
                        .join('');
                    
                    if (iteration >= originalText.length) {
                        clearInterval(interval);
                    }
                    
                    iteration += 1 / 3;
                }, 30);
            }

            // Delay the scramble effect
            setTimeout(scrambleText, 1000);
        }

        // Initialize everything
        function init() {
            // Ensure text is visible immediately
            document.querySelector('.hero-title').style.opacity = '1';
            document.querySelector('.hero-subtitle').style.opacity = '1';
            document.querySelector('.hero-actions').style.opacity = '1';

            animate();
            initAnimations();
            initMicrointeractions();
            initParallax();
            initTextEffects();

            // Hide loading screen after everything is ready
            setTimeout(hideLoadingScreen, 500);
        }

        // Start when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Show text immediately
            setTimeout(() => {
                const heroTitle = document.querySelector('.hero-title');
                const heroSubtitle = document.querySelector('.hero-subtitle');
                const heroActions = document.querySelector('.hero-actions');

                if (heroTitle) heroTitle.style.opacity = '1';
                if (heroSubtitle) heroSubtitle.style.opacity = '1';
                if (heroActions) heroActions.style.opacity = '1';
            }, 100);

            init();
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Performance optimization
        let ticking = false;
        function updateParallax() {
            // Throttle scroll events
            if (!ticking) {
                requestAnimationFrame(() => {
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', updateParallax, { passive: true });

        // Mobile menu toggle (basic implementation)
        function initMobileMenu() {
            const navbar = document.querySelector('.navbar');
            if (window.innerWidth <= 768) {
                navbar.innerHTML += `
                    <button class="mobile-menu-toggle" style="display: block; background: none; border: none; font-size: 1.5rem; color: var(--color-primary); cursor: pointer;">
                        ☰
                    </button>
                `;
            }
        }

        window.addEventListener('resize', initMobileMenu);
        initMobileMenu();

        // Add some easter eggs for engagement
        let konami = [];
        const konamiCode = [38, 38, 40, 40, 37, 39, 37, 39, 66, 65];

        document.addEventListener('keydown', (e) => {
            konami.push(e.keyCode);
            if (konami.length > konamiCode.length) {
                konami.shift();
            }
            
            if (konami.join('') === konamiCode.join('')) {
                // Easter egg: Make all shapes rainbow colored
                shapes.forEach(shape => {
                    shape.material = new THREE.MeshLambertMaterial({ 
                        color: Math.random() * 0xffffff,
                        transparent: true,
                        opacity: 0.8
                    });
                });
                
                // Show a fun message
                gsap.to('.hero-title', {
                    duration: 0.5,
                    color: '#ff6b6b',
                    scale: 1.1,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
            }
        });
    </script>
</body>
</html>